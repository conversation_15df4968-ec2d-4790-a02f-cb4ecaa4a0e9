<?php
/**
 * Bildirim Butonu Template
 * Bu dosya, dashboard header'a bildirim butonunu ekler.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

// Do<PERSON>rudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Bildirim Butonu -->
<div class="tutor-notification-toggle">
    <button class="tutor-notification-button" aria-label="Bildirimleri Aç" title="Bildirimler" id="tutor-notification-button">
        <!-- Bell ikonu -->
        <svg class="tutor-notification-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
        <!-- B<PERSON><PERSON><PERSON> say<PERSON> badge'i (şimdilik gizli) -->
        <span class="tutor-notification-badge" id="tutor-notification-badge" style="display: none;">0</span>
    </button>
</div>
