(()=>{var n={};var e;function t(){if(!e){e=i("meta_data","client")}return e}function r(n){return new Promise((function(e,t){n.oncomplete=n.onsuccess=function(){return e(n.result)};n.onabort=n.onerror=function(){return t(n.error)}}))}function i(n,e){var t=indexedDB.open(n);t.onupgradeneeded=function(){return t.result.createObjectStore(e)};var i=r(t);return function(n,t){return i.then((function(r){return t(r.transaction(e,n).objectStore(e))}))}}function o(n){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:t();return e("readonly",(function(e){return r(e.get(n))}))}function c(n,e){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:t();return i("readwrite",(function(t){t.put(e,n);return r(t.transaction)}))}self.addEventListener("push",(function(n){if(!self.registration){console.warn("Notification not Registered.");return}var e=n.data.json();o("client_id").then((function(n){o("browser_key").then((function(t){var r=!e.client_id||!n||e.client_id!=n;var i=!e.browser_key||!t||e.browser_key!=t;if(i||r){return}self.registration.showNotification(e.title,e)}))}))}));self.addEventListener("notificationclick",(function(n){if(n.notification.data.url){clients.openWindow(n.notification.data.url)}n.notification.close()}));self.addEventListener("message",(function(n){var e=JSON.parse(n.data);c("client_id",e.client_id).then((function(){}))["catch"]((function(n){return console.warn("Client ID saving failed!",n)}));c("browser_key",e.browser_key).then((function(){}))["catch"]((function(n){return console.warn("Browser ID saving failed!",n)}))}))})();