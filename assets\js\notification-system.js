/**
 * Bildirim Sistemi JavaScript
 * Bu dosya, bildirim butonu ve sidebar fonksiyonalitesini yönetir.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

(function() {
    'use strict';

    // DOM yüklendikten sonra çalıştır
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationSystem();
    });

    /**
     * Bildirim sistemini başlat
     */
    function initNotificationSystem() {
        const notificationButton = document.getElementById('tutor-notification-button');
        const notificationOverlay = document.getElementById('tutor-notification-overlay');
        const notificationSidebar = document.getElementById('tutor-notification-sidebar');
        const notificationClose = document.getElementById('tutor-notification-close');

        // Elementlerin varlığını kontrol et
        if (!notificationButton || !notificationOverlay || !notificationSidebar || !notificationClose) {
            console.warn('Bildirim sistemi elementleri bulunamadı');
            return;
        }

        // Event listener'ları ekle
        setupEventListeners(notificationButton, notificationOverlay, notificationSidebar, notificationClose);

        // Klavye erişilebilirliği
        setupKeyboardAccessibility(notificationOverlay, notificationClose);

        console.log('Bildirim sistemi başarıyla başlatıldı');
    }

    /**
     * Event listener'ları ayarla
     */
    function setupEventListeners(button, overlay, sidebar, closeBtn) {
        // Bildirim butonuna tıklama
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, true);
        });

        // Kapatma butonuna tıklama
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, false);
        });

        // Overlay'e tıklama (sidebar dışına tıklama)
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Escape tuşu ile kapatma
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && overlay.classList.contains('active')) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Sayfa scroll'unu engelle (sidebar açıkken)
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });
    }

    /**
     * Klavye erişilebilirliğini ayarla
     */
    function setupKeyboardAccessibility(overlay, closeBtn) {
        // Tab tuşu ile focus yönetimi
        overlay.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                trapFocus(e, overlay);
            }
        });

        // İlk focus'u kapatma butonuna ver
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                closeBtn.focus();
            }
        });
    }

    /**
     * Focus'u sidebar içinde tut
     */
    function trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * Bildirim sidebar'ını aç/kapat
     */
    function toggleNotificationSidebar(overlay, sidebar, show) {
        const button = document.getElementById('tutor-notification-button');

        if (show) {
            // Sidebar'ı aç
            overlay.classList.add('active');

            // Animasyon için kısa gecikme
            setTimeout(() => {
                sidebar.classList.add('active');
            }, 10);

            // Buton aktif durumunu ekle
            if (button) {
                button.classList.add('active');
            }

            // Buton animasyonu
            animateNotificationButton(true);

            // Aria durumunu güncelle
            updateAriaStates(true);

        } else {
            // Sidebar'ı kapat
            sidebar.classList.remove('active');
            overlay.classList.remove('active');

            // Buton aktif durumunu kaldır
            if (button) {
                button.classList.remove('active');
            }

            // Buton animasyonu
            animateNotificationButton(false);

            // Aria durumunu güncelle
            updateAriaStates(false);

            // Body scroll'unu geri yükle
            document.body.style.overflow = '';
        }
    }

    /**
     * Bildirim butonuna animasyon ekle
     */
    function animateNotificationButton(isOpening) {
        const button = document.getElementById('tutor-notification-button');
        if (!button) return;

        if (isOpening) {
            // Açılma animasyonu
            button.style.transform = 'scale(0.9)';
            button.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        } else {
            // Kapanma animasyonu
            button.style.transform = 'scale(1.1)';
            button.style.backgroundColor = '';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * ARIA durumlarını güncelle
     */
    function updateAriaStates(isOpen) {
        const button = document.getElementById('tutor-notification-button');
        const overlay = document.getElementById('tutor-notification-overlay');

        if (button) {
            button.setAttribute('aria-expanded', isOpen.toString());
            button.setAttribute('aria-label', isOpen ? 'Bildirimleri Kapat' : 'Bildirimleri Aç');
        }

        if (overlay) {
            overlay.setAttribute('aria-hidden', (!isOpen).toString());
        }
    }

    /**
     * Bildirim sayısını güncelle (gelecekte kullanılacak)
     */
    function updateNotificationCount(count) {
        const badge = document.getElementById('tutor-notification-badge');
        if (!badge) return;

        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    /**
     * Bildirim listesini güncelle (gelecekte kullanılacak)
     */
    function updateNotificationList(notifications) {
        const list = document.getElementById('tutor-notification-list');
        const empty = document.getElementById('tutor-notification-empty');
        const markAllBtn = document.getElementById('tutor-notification-mark-all-read');

        if (!list || !empty || !markAllBtn) return;

        if (notifications && notifications.length > 0) {
            // Bildirimleri göster
            empty.style.display = 'none';
            list.style.display = 'block';
            markAllBtn.style.display = 'flex';

            // Bildirim HTML'ini oluştur (şimdilik boş)
            list.innerHTML = '';
        } else {
            // Boş durumu göster
            empty.style.display = 'flex';
            list.style.display = 'none';
            markAllBtn.style.display = 'none';
        }
    }

    /**
     * Global fonksiyonları window objesine ekle (gelecekte kullanım için)
     */
    window.TutorNotificationSystem = {
        updateCount: updateNotificationCount,
        updateList: updateNotificationList,
        toggle: function(show) {
            const overlay = document.getElementById('tutor-notification-overlay');
            const sidebar = document.getElementById('tutor-notification-sidebar');
            if (overlay && sidebar) {
                toggleNotificationSidebar(overlay, sidebar, show);
            }
        }
    };

})();
