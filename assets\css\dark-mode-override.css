/**
 * Dark Mode Override CSS
 * B<PERSON> dosya, dark mode için en yüksek öncelikli stilleri içerir.
 * !important kullanarak diğer stilleri geçersiz kılar.
 */

/* Kurs izleme ekranı için dark mode arka plan rengi */
html[data-theme="dark"] body.tutor-course-viewing-page,
body.tutor-dark-mode.tutor-course-viewing-page,
html[data-theme="dark"] .tutor-course-viewing-page,
.tutor-dark-mode.tutor-course-viewing-page,
html[data-theme="dark"] .tutor-course-single-content-wrapper,
.tutor-dark-mode .tutor-course-single-content-wrapper,
html[data-theme="dark"] #tutor-single-entry-content,
body.tutor-dark-mode #tutor-single-entry-content {
    background-color: #0F0F0F !important;
}

/* Ana içerik alanı için dark mode stilleri */
html[data-theme="dark"] #tutor-single-entry-content,
body.tutor-dark-mode #tutor-single-entry-content {
    color: #F5F5F5 !important;
}

/* Dark mode için sidebar arka plan rengi */
html[data-theme="dark"] body .tutor-course-single-sidebar-wrapper,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper {
    background-color: #0F0F0F !important;
}

/* Dark mode için sidebar başlık arka plan rengi */
html[data-theme="dark"] .tutor-course-single-sidebar-title,
body.tutor-dark-mode .tutor-course-single-sidebar-title {
    background-color: #0F0F0F !important;
}

/* Dark mode için kurs konu öğeleri arka plan rengi */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a {
    background-color: #1e1e1e !important;
}

/* Dark mode için accordion stilleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body {
    background-color: #1E1E1E !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header {
    background-color: #1E1E1E !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Dark mode için aktif accordion başlığındaki ok ikonu */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header.is-active:after,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header.is-active:after {
    color: #ffffff !important;
}

/* Dark mode için kurs içerik öğeleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-content-list-item,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-content-list-item {
    background-color: #1E1E1E !important;
    border-color: #333333 !important;
}

/* Dark mode için aktif ders öğesi */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-content-list-item.tutor-active,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-content-list-item.tutor-active,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active {
    background-color: #2F2F2F !important;
    border-left-color: #3E64DE !important;
}

/* Dark mode için açıklama metni rengi */
html[data-theme="dark"] .tutor-course-topic-description,
body.tutor-dark-mode .tutor-course-topic-description {
    color: #FFFFFF6E !important;
}

/* Dark mode için metin renkleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-title,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-title,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header-title,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header-title,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-content-list-item-title,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-content-list-item-title {
    color: #FFFFFF !important;
}

/* Dark mode için kurs konu öğesi başlık rengi */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item-title,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item-title {
    color: #FFFFFF !important;
}

/* Dark mode için hover ve aktif durumdaki kurs konu öğeleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-icon,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-title,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-icon,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-title,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-icon,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-title,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-icon,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-title {
    color: #FFFFFF !important;
}

/* Dark mode için aktif kurs konu öğesi bağlantıları */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active a,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active a {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 30%, transparent) !important;
}

/* Dark mode için hover durumundaki kurs konu öğesi bağlantıları */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a:hover,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a:hover {
    background-color: #ffffff08 !important;
}

/* Dark mode için soluk metin rengi - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-color-muted,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-color-muted,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-color-muted,
.tutor-dark-mode.tutor-course-viewing-page .tutor-color-muted,
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-color-secondary,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-color-secondary,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-color-secondary,
.tutor-dark-mode.tutor-course-viewing-page .tutor-color-secondary {
    color: #ffffffb5 !important;
}

/* Dark mode için dashboard meta değerleri */
html[data-theme="dark"] .tutor-meta-value,
html[data-theme="dark"] .tutor-meta a,
body.tutor-dark-mode .tutor-meta-value,
body.tutor-dark-mode .tutor-meta a {
    color: #ffffff75 !important;
}

/* Dark mode için dashboard tutor-wrap metin rengi */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-wrap,
body.tutor-dark-mode.tutor-dashboard-page .tutor-wrap {
    color: #ffffff7a !important;
}

/* Dark mode için modal içerik arka plan rengi - sadece dashboard sayfalarında */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-modal-content-white,
body.tutor-dark-mode.tutor-dashboard-page .tutor-modal-content-white,
html[data-theme="dark"] .tutor-dashboard-page .tutor-modal-content-white,
.tutor-dark-mode.tutor-dashboard-page .tutor-modal-content-white {
    background-color: #121212 !important;
}

/* Dark mode için modal içerik arka plan rengi - sadece dashboard sayfalarında */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-modal-content,
body.tutor-dark-mode.tutor-dashboard-page .tutor-modal-content,
html[data-theme="dark"] .tutor-dashboard-page .tutor-modal-content,
.tutor-dark-mode.tutor-dashboard-page .tutor-modal-content {
    background-color: #121212 !important;
}

/* Dark mode için modal başlık ve footer arka plan rengi - sadece dashboard sayfalarında */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-modal-header,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-modal-footer,
body.tutor-dark-mode.tutor-dashboard-page .tutor-modal-header,
body.tutor-dark-mode.tutor-dashboard-page .tutor-modal-footer,
html[data-theme="dark"] .tutor-dashboard-page .tutor-modal-header,
html[data-theme="dark"] .tutor-dashboard-page .tutor-modal-footer,
.tutor-dark-mode.tutor-dashboard-page .tutor-modal-header,
.tutor-dark-mode.tutor-dashboard-page .tutor-modal-footer {
    background-color: #1e1e1e !important;
}

/* Dark mode için modal başlık metin rengi - sadece dashboard sayfalarında */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-modal-title,
body.tutor-dark-mode.tutor-dashboard-page .tutor-modal-title,
html[data-theme="dark"] .tutor-dashboard-page .tutor-modal-title,
.tutor-dark-mode.tutor-dashboard-page .tutor-modal-title {
    color: #ffffff !important;
}

/* Dark mode için dashboard toggle label rengi */
html[data-theme="dark"] .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-qna-vew-as.current-view-instructor .tutor-form-toggle-label.tutor-form-toggle-unchecked,
body.tutor-dark-mode .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-qna-vew-as.current-view-instructor .tutor-form-toggle-label.tutor-form-toggle-unchecked {
    color: #ffffff85 !important;
}

/* Dark mode için form select dropdown arka plan rengi */
html[data-theme="dark"] .tutor-form-select-dropdown,
body.tutor-dark-mode .tutor-form-select-dropdown {
    background: #1e1e1e !important;
    border: 1px solid #cdcfd54a !important;
}

/* Dark mode için form select option hover ve aktif durumu */
html[data-theme="dark"] .tutor-form-select-option:hover,
html[data-theme="dark"] .tutor-form-select-option.is-active,
body.tutor-dark-mode .tutor-form-select-option:hover,
body.tutor-dark-mode .tutor-form-select-option.is-active {
    background: #4b4b4b !important;
}

/* Dark mode için navigasyon kenarlık rengi - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs),
body.tutor-dark-mode.tutor-course-viewing-page .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs),
html[data-theme="dark"] .tutor-course-viewing-page .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs),
.tutor-dark-mode.tutor-course-viewing-page .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
    border-bottom: 1px dashed #ffffff24 !important;
}

/* Kurs detay sayfasındaki navigasyon yazıları dark mode'da siyah kalsın */
html[data-theme="dark"] .tutor-course-details-tab .tutor-is-sticky nav .tutor-nav-link,
body.tutor-dark-mode .tutor-course-details-tab .tutor-is-sticky nav .tutor-nav-link {
    color: #212327 !important;
}

/* Dark mode için ikon renkleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-icon-angle-right,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-icon-angle-right,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-icon-angle-down,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-icon-angle-down,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-icon-document,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-icon-document,
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-icon-play,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-icon-play {
    color: #FFFFFF !important;
}

/* Dark mode için arama kutusu stilleri */
html[data-theme="dark"] #tutor-course-content-search,
body.tutor-dark-mode #tutor-course-content-search {
    background-color: #1E1E1E !important;
    border-color: #333333 !important;
    color: #F5F5F5 !important;
}

/* Dark mode için arama sonuçları stilleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic {
    background-color: #0F0F0F !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic-item,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic-item {
    background-color: #1E1E1E !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic-item a,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic-item a {
    background-color: #1E1E1E !important;
    color: #FFFFFF !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-accordion-item-header,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-accordion-item-header {
    background-color: #1E1E1E !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-accordion-item-body,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-accordion-item-body {
    background-color: #1E1E1E !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-content-list-item,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-content-list-item {
    background-color: #1E1E1E !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-search-visible,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching .tutor-search-visible {
    display: block !important;
    background-color: #1E1E1E !important;
}

/* Dark mode için arama ikonu */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-icon-search,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-icon-search {
    color: #FFFFFF !important;
}

/* Dark mode için arama çarpı ikonu */
html[data-theme="dark"] .tutor-search-icon.tutor-icon-times,
body.tutor-dark-mode .tutor-search-icon.tutor-icon-times,
html[data-theme="dark"] #tutor-course-search-icon,
body.tutor-dark-mode #tutor-course-search-icon {
    color: #ffffff54 !important;
}

/* Dark mode için arama sonuçları bulunamadı mesajı */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper.tutor-searching.tutor-search-no-results::after,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper.tutor-searching.tutor-search-no-results::after {
    color: #FFFFFF !important;
    background-color: #1E1E1E !important;
}

/* Dark mode için arama sonuçları bulunamadı bileşeni */
html[data-theme="dark"] .tutor-no-search-results,
body.tutor-dark-mode .tutor-no-search-results {
    background-color: #1E1E1E !important;
}

html[data-theme="dark"] .tutor-no-search-results-text,
body.tutor-dark-mode .tutor-no-search-results-text {
    color: #ffffffde !important;
}

html[data-theme="dark"] .tutor-no-search-results-hint,
body.tutor-dark-mode .tutor-no-search-results-hint {
    color: #ffffff6b !important;
}

html[data-theme="dark"] .tutor-no-search-results-icon,
body.tutor-dark-mode .tutor-no-search-results-icon {
    background-color: #313131 !important;
}

/* Dark mode için ilerleme çubuğu - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-progress-bar,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-progress-bar,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-progress-bar,
.tutor-dark-mode.tutor-course-viewing-page .tutor-progress-bar {
    background-color: #333333 !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-progress-bar .tutor-progress-value,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-progress-bar .tutor-progress-value,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-progress-bar .tutor-progress-value,
.tutor-dark-mode.tutor-course-viewing-page .tutor-progress-bar .tutor-progress-value {
    background-color: #3E64DE !important;
}

/* Dark mode için hamburger menü ikonu */
html[data-theme="dark"] body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-icon-hamburger-menu,
body.tutor-dark-mode body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-icon-hamburger-menu {
    color: #ffffffa6 !important;
}

/* Dark mode için iconic buton */
html[data-theme="dark"] body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn,
body.tutor-dark-mode body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn {
    color: #ffffffa6 !important;
}

/* Dark mode için butonlar - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-btn,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-btn,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-btn,
.tutor-dark-mode.tutor-course-viewing-page .tutor-btn {
    background-color: #1E1E1E !important;
    border: none !important;
    color: #F5F5F5 !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-btn-primary,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-btn-primary,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-btn-primary,
.tutor-dark-mode.tutor-course-viewing-page .tutor-btn-primary {
    background-color: #3E64DE !important;
    border-color: #3E64DE !important;
    color: #FFFFFF !important;
}

/* Dark mode için başarı butonu */
html[data-theme="dark"] body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-btn-success.tutor-topbar-mark-btn,
body.tutor-dark-mode body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-btn-success.tutor-topbar-mark-btn {
    color: #bbfdb3 !important;
    background-color: #0c5b0261 !important;
}

/*--------------------------------------------------------------
TOAST NOTIFICATION VE ALERT SİSTEMLERİ - DARK MODE
--------------------------------------------------------------*/

/**
 * TOAST NOTIFICATION DARK MODE STİLLERİ
 * Bu bölüm, para çekme ve diğer AJAX işlemlerinde görünen toast notification
 * mesajlarının dark mode'da uygun görünmesini sağlar.
 */

/* Tutor toast notification - dark mode */
html[data-theme="dark"] .tutor-toast,
body.tutor-dark-mode .tutor-toast,
html[data-theme="dark"] .tutor-notification,
body.tutor-dark-mode .tutor-notification,
html[data-theme="dark"] .tutor-toast-success,
body.tutor-dark-mode .tutor-toast-success,
html[data-theme="dark"] .tutor-toast-message,
body.tutor-dark-mode .tutor-toast-message {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Toast notification başarı mesajları - dark mode */
html[data-theme="dark"] .tutor-toast.tutor-success,
body.tutor-dark-mode .tutor-toast.tutor-success,
html[data-theme="dark"] .tutor-notification.tutor-success,
body.tutor-dark-mode .tutor-notification.tutor-success,
html[data-theme="dark"] .tutor-alert-success,
body.tutor-dark-mode .tutor-alert-success {
    background-color: #1e3a1e !important;
    color: #a8e6a3 !important;
    border-color: #2d5a2d !important;
}

/* Toast notification hata mesajları - dark mode */
html[data-theme="dark"] .tutor-toast.tutor-error,
body.tutor-dark-mode .tutor-toast.tutor-error,
html[data-theme="dark"] .tutor-notification.tutor-error,
body.tutor-dark-mode .tutor-notification.tutor-error,
html[data-theme="dark"] .tutor-alert-error,
body.tutor-dark-mode .tutor-alert-error {
    background-color: #3a1e1e !important;
    color: #ffb3b3 !important;
    border-color: #5a2d2d !important;
}

/* Toast notification uyarı mesajları - dark mode */
html[data-theme="dark"] .tutor-toast.tutor-warning,
body.tutor-dark-mode .tutor-toast.tutor-warning,
html[data-theme="dark"] .tutor-notification.tutor-warning,
body.tutor-dark-mode .tutor-notification.tutor-warning,
html[data-theme="dark"] .tutor-alert-warning,
body.tutor-dark-mode .tutor-alert-warning {
    background-color: #3a3a1e !important;
    color: #ffe6a3 !important;
    border-color: #5a5a2d !important;
}

/* Toast notification bilgi mesajları - dark mode */
html[data-theme="dark"] .tutor-toast.tutor-info,
body.tutor-dark-mode .tutor-toast.tutor-info,
html[data-theme="dark"] .tutor-notification.tutor-info,
body.tutor-dark-mode .tutor-notification.tutor-info,
html[data-theme="dark"] .tutor-alert-info,
body.tutor-dark-mode .tutor-alert-info {
    background-color: #1e2a3a !important;
    color: #a3c7e6 !important;
    border-color: #2d3d5a !important;
}

/* Genel alert sistemi - dark mode */
html[data-theme="dark"] .tutor-alert,
body.tutor-dark-mode .tutor-alert {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border-color: #3a3a3a !important;
}

/* Alert metin içeriği - dark mode */
html[data-theme="dark"] .tutor-alert-text,
body.tutor-dark-mode .tutor-alert-text {
    color: #ffffff !important;
}

/* Alert ikonu - dark mode */
html[data-theme="dark"] .tutor-alert-icon,
body.tutor-dark-mode .tutor-alert-icon {
    color: #ffffff !important;
}

/* Alert kapatma butonu - dark mode */
html[data-theme="dark"] .tutor-alert-close,
body.tutor-dark-mode .tutor-alert-close {
    color: #ffffff6b !important;
}

/* Sağ alt köşe toast notification konteyneri - dark mode */
html[data-theme="dark"] .toast-container,
body.tutor-dark-mode .toast-container,
html[data-theme="dark"] .notification-container,
body.tutor-dark-mode .notification-container {
    background-color: transparent !important;
}

/* Toast notification wrapper - dark mode */
html[data-theme="dark"] .toast-wrapper,
body.tutor-dark-mode .toast-wrapper,
html[data-theme="dark"] .notification-wrapper,
body.tutor-dark-mode .notification-wrapper {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    border-radius: 6px !important;
}

/* Toast notification başlık - dark mode */
html[data-theme="dark"] .toast-title,
body.tutor-dark-mode .toast-title,
html[data-theme="dark"] .notification-title,
body.tutor-dark-mode .notification-title {
    color: #ffffff !important;
}

/* Toast notification mesaj - dark mode */
html[data-theme="dark"] .toast-message,
body.tutor-dark-mode .toast-message,
html[data-theme="dark"] .notification-message,
body.tutor-dark-mode .notification-message {
    color: #ffffffde !important;
}

/* Dashboard sayfalarında toast notification - dark mode */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-toast,
body.tutor-dark-mode.tutor-dashboard-page .tutor-toast,
html[data-theme="dark"] .tutor-dashboard-page .tutor-toast,
.tutor-dark-mode.tutor-dashboard-page .tutor-toast {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Dashboard sayfalarında alert - dark mode */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-alert,
body.tutor-dark-mode.tutor-dashboard-page .tutor-alert,
html[data-theme="dark"] .tutor-dashboard-page .tutor-alert,
.tutor-dark-mode.tutor-dashboard-page .tutor-alert {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border-color: #3a3a3a !important;
}

/* Para çekme sayfasında notification - dark mode */
html[data-theme="dark"] .tutor-frontend-dashboard-withdrawal .tutor-toast,
body.tutor-dark-mode .tutor-frontend-dashboard-withdrawal .tutor-toast,
html[data-theme="dark"] .tutor-frontend-dashboard-withdrawal .tutor-notification,
body.tutor-dark-mode .tutor-frontend-dashboard-withdrawal .tutor-notification {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Swal2 (SweetAlert2) toast notification - dark mode */
html[data-theme="dark"] .swal2-toast,
body.tutor-dark-mode .swal2-toast {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Swal2 toast başlık - dark mode */
html[data-theme="dark"] .swal2-toast .swal2-title,
body.tutor-dark-mode .swal2-toast .swal2-title {
    color: #ffffff !important;
}

/* Swal2 toast içerik - dark mode */
html[data-theme="dark"] .swal2-toast .swal2-html-container,
body.tutor-dark-mode .swal2-toast .swal2-html-container {
    color: #ffffffde !important;
}

/* jQuery toast notification - dark mode */
html[data-theme="dark"] .jq-toast-wrap,
body.tutor-dark-mode .jq-toast-wrap {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Genel notification ve popup elementleri - dark mode */
html[data-theme="dark"] .notification,
body.tutor-dark-mode .notification,
html[data-theme="dark"] .popup-notification,
body.tutor-dark-mode .popup-notification,
html[data-theme="dark"] .success-message,
body.tutor-dark-mode .success-message,
html[data-theme="dark"] .error-message,
body.tutor-dark-mode .error-message,
html[data-theme="dark"] .warning-message,
body.tutor-dark-mode .warning-message,
html[data-theme="dark"] .info-message,
body.tutor-dark-mode .info-message {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Başarı mesajları için özel renk - dark mode */
html[data-theme="dark"] .success-message,
body.tutor-dark-mode .success-message,
html[data-theme="dark"] .notification.success,
body.tutor-dark-mode .notification.success {
    background-color: #1e3a1e !important;
    color: #a8e6a3 !important;
    border-color: #2d5a2d !important;
}

/* Hata mesajları için özel renk - dark mode */
html[data-theme="dark"] .error-message,
body.tutor-dark-mode .error-message,
html[data-theme="dark"] .notification.error,
body.tutor-dark-mode .notification.error {
    background-color: #3a1e1e !important;
    color: #ffb3b3 !important;
    border-color: #5a2d2d !important;
}

/* Uyarı mesajları için özel renk - dark mode */
html[data-theme="dark"] .warning-message,
body.tutor-dark-mode .warning-message,
html[data-theme="dark"] .notification.warning,
body.tutor-dark-mode .notification.warning {
    background-color: #3a3a1e !important;
    color: #ffe6a3 !important;
    border-color: #5a5a2d !important;
}

/* Bilgi mesajları için özel renk - dark mode */
html[data-theme="dark"] .info-message,
body.tutor-dark-mode .info-message,
html[data-theme="dark"] .notification.info,
body.tutor-dark-mode .notification.info {
    background-color: #1e2a3a !important;
    color: #a3c7e6 !important;
    border-color: #2d3d5a !important;
}

/* Notification kapatma butonu - dark mode */
html[data-theme="dark"] .notification-close,
body.tutor-dark-mode .notification-close,
html[data-theme="dark"] .toast-close,
body.tutor-dark-mode .toast-close {
    color: #ffffff6b !important;
    background-color: transparent !important;
    border: none !important;
}

/* Notification kapatma butonu hover - dark mode */
html[data-theme="dark"] .notification-close:hover,
body.tutor-dark-mode .notification-close:hover,
html[data-theme="dark"] .toast-close:hover,
body.tutor-dark-mode .toast-close:hover {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Bildirim ikonu margin override - Orijinal Tutor LMS'den gelen margin-right'ı engelle */
.tutor-notification-icon {
    margin-right: 0 !important;
}


/* Dark mode için normal mark butonu */
html[data-theme="dark"] body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-topbar-mark-btn,
body.tutor-dark-mode body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-topbar-mark-btn {
    color: #cbd7ff !important;
    background-color: #001b70 !important;
}

/* Dark mode için checkbox stilleri */
html[data-theme="dark"] .tutor-form-check-input.tutor-form-check-input:not(:checked),
body.tutor-dark-mode .tutor-form-check-input.tutor-form-check-input:not(:checked) {
    background-color: #ffffff2b !important;
    border: none !important;
}

/* Dark mode için yorum bileşenleri */
html[data-theme="dark"] .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comments-list .tutor-single-comment .tutor-actual-comment,
body.tutor-dark-mode .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comments-list .tutor-single-comment .tutor-actual-comment {
    background-color: #1e1e1e !important;
    border: 1px solid #ffffff14 !important;
}

html[data-theme="dark"] .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comments-list .tutor-single-comment .tutor-actual-comment .tutor-comment-author span:first-child,
body.tutor-dark-mode .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comments-list .tutor-single-comment .tutor-actual-comment .tutor-comment-author span:first-child {
    color: #ffffff !important;
}

/* Dark mode için form kontrolleri */
html[data-theme="dark"] .tutor-form-control,
body.tutor-dark-mode .tutor-form-control {
    background-color: #1e1e1e !important;
    border: 1px solid #ffffff14 !important;
    color: #ffffff !important;
}

/* Dark mode için form etiketleri */
html[data-theme="dark"] .tutor-form-label,
body.tutor-dark-mode .tutor-form-label {
    color: #ffffff80 !important;
}

/* Dark mode için radio select */
html[data-theme="dark"] .tutor-radio-select,
body.tutor-dark-mode .tutor-radio-select {
    background: #1e1e1e !important;
}

html[data-theme="dark"] .tutor-radio-select .tutor-radio-select-content .tutor-radio-select-title,
body.tutor-dark-mode .tutor-radio-select .tutor-radio-select-content .tutor-radio-select-title {
    color: #ffffff !important;
}

/* Dark mode için sosyal medya alanları */
html[data-theme="dark"] .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field>div:first-child,
body.tutor-dark-mode .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field>div:first-child {
    color: #ffffff !important;
}

html[data-theme="dark"] .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field>div:first-child i,
body.tutor-dark-mode .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field>div:first-child i {
    color: #ffffff63 !important;
}

/* Dark mode için yorum kutusu */
html[data-theme="dark"] .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comment-box .tutor-comment-textarea,
body.tutor-dark-mode .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comment-box .tutor-comment-textarea {
    border: none !important;
}

/* Dark mode için konuşma bileşeni */
html[data-theme="dark"] .tutor-course-spotlight-wrapper .tutor-conversation,
body.tutor-dark-mode .tutor-course-spotlight-wrapper .tutor-conversation {
    border-bottom: none !important;
}

/* Dark mode için kurs izleme ekranında kart stilleri */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-card,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-card,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-card,
.tutor-dark-mode.tutor-course-viewing-page .tutor-card {
    background-color: #1e1e1e !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-card:not(.tutor-no-border),
body.tutor-dark-mode.tutor-course-viewing-page .tutor-card:not(.tutor-no-border),
html[data-theme="dark"] .tutor-course-viewing-page .tutor-card:not(.tutor-no-border),
.tutor-dark-mode.tutor-course-viewing-page .tutor-card:not(.tutor-no-border) {
    border: none !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-color-black,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-color-black,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-color-black,
.tutor-dark-mode.tutor-course-viewing-page .tutor-color-black {
    color: #ffffff !important;
}


/* Dark mode için tablo stilleri - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-table tr td,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr td,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-table tr td,
.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr td {
    color: #ffffff !important;
    border-bottom: 1px dashed #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-table tr td:first-child,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr td:first-child,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-table tr td:first-child,
.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr td:first-child {
    border-left: 1px dashed #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-table tr td:last-child,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr td:last-child,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-table tr td:last-child,
.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr td:last-child {
    border-right: 1px dashed #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-table tr th,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr th,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-table tr th,
.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr th {
    background: #1a1a1a !important;
    color: #ffffff !important;
    border-bottom: 1px dashed #cdcfd54d !important;
    border-top: 1px dashed #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-table tr th:first-child,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr th:first-child,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-table tr th:first-child,
.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr th:first-child {
    border-left: 1px dashed #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page .tutor-table tr th:last-child,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr th:last-child,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-table tr th:last-child,
.tutor-dark-mode.tutor-course-viewing-page .tutor-table tr th:last-child {
    border-right: 1px dashed #cdcfd54d !important;
}

/* Dark mode için quiz sonuç özeti - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-result-summary,
body.tutor-dark-mode.tutor-course-viewing-page .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-result-summary,
html[data-theme="dark"] .tutor-course-viewing-page .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-result-summary,
.tutor-dark-mode.tutor-course-viewing-page .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-quiz-wrapper .tutor-quiz-result-summary {
    border-top: 1px dashed #cdcfd54d !important;
    border-bottom: 1px dashed #cdcfd54d !important;
}

/* Dark mode için quiz eşleştirme tablosu - sadece kurs izleme ekranında */
html[data-theme="dark"] body.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td,
body.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td,
html[data-theme="dark"] .tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td,
.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td {
    border-bottom: 1px solid #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:first-child,
body.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:first-child,
html[data-theme="dark"] .tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:first-child,
.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:first-child {
    border-left: 1px solid #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:last-child,
body.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:last-child,
html[data-theme="dark"] .tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:last-child,
.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr td:last-child {
    border-right: 1px solid #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th,
body.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th,
html[data-theme="dark"] .tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th,
.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th {
    border-bottom: 1px solid #cdcfd54d !important;
    border-top: 1px solid #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:first-child,
body.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:first-child,
html[data-theme="dark"] .tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:first-child,
.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:first-child {
    border-left: 1px solid #cdcfd54d !important;
}

html[data-theme="dark"] body.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:last-child,
body.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:last-child,
html[data-theme="dark"] .tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:last-child,
.tutor-dark-mode.tutor-course-viewing-page #tutor-quiz-image-matching-choice .tutor-table-responsive.tutor-table-mobile .tutor-table tr th:last-child {
    border-right: 1px solid #cdcfd54d !important;
}

/* Dark mode için datepicker stilleri */
html[data-theme="dark"] .tutor-react-datepicker .react-datepicker,
body.tutor-dark-mode .tutor-react-datepicker .react-datepicker {
    background: #1e1e1e !important;
}

html[data-theme="dark"] .tutor-react-datepicker .dropdown-container .dropdown-label,
body.tutor-dark-mode .tutor-react-datepicker .dropdown-container .dropdown-label {
    color: #ffffff !important;
}

html[data-theme="dark"] .tutor-react-datepicker__selects-range .react-datepicker__custom-footer,
body.tutor-dark-mode .tutor-react-datepicker__selects-range .react-datepicker__custom-footer {
    border-top: 1px solid #0f0f0f !important;
}

/* Dark mode için mobil görünüm */
@media (max-width: 1199px) {
    html[data-theme="dark"] body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header,
    body.tutor-dark-mode .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header {
        background-color: #1E1E1E !important;
    }

    html[data-theme="dark"] .tutor-course-single-content-wrapper.tutor-course-single-sidebar-open .tutor-course-single-sidebar-wrapper,
    body.tutor-dark-mode .tutor-course-single-content-wrapper.tutor-course-single-sidebar-open .tutor-course-single-sidebar-wrapper {
        background-color: #0f0f0f !important;
    }
}
